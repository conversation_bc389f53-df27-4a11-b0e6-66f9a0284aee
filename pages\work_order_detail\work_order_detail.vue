<template>
	<view class="work-order-detail-container">
		<!-- 案件基本信息 -->
		<view class="work-order-card">
			<view class="work-order-header">
				<view class="work-order-title">
					<text>调解案件号: </text>
					<text class="work-order-id">{{workOrderData.case_number}}</text>
				</view>
				<view class="work-order-status">
					<text class="status-label" :class="{'status-pending': workOrderData.case_status_cn === '待确认'}">{{workOrderData.case_status_cn || '待确认'}}</text>
				</view>
			</view>
			<view class="work-order-date">发起日期: {{workOrderData.initiate_date || '2023-11-01'}}</view>
			<view class="work-order-date" v-if="workOrderData.case_status_cn === '已关闭'">关闭日期: {{workOrderData.closingDate || '2023-12-01'}}</view>
		</view>
		
		<!-- 进度条 -->
		<view class="progress-bar">
			<view class="progress-steps">
				<view class="progress-step active">
					<view class="step-circle">1</view>
					<view class="step-line"></view>
					<view class="step-label">调解确认</view>
				</view>
				<view class="progress-step">
					<view class="step-circle">2</view>
					<view class="step-line"></view>
					<view class="step-label">方案确认</view>
				</view>
				<view class="progress-step">
					<view class="step-circle">3</view>
					<view class="step-line"></view>
					<view class="step-label">协议签署</view>
				</view>
				<view class="progress-step">
					<view class="step-circle">4</view>
					<view class="step-label">完成</view>
				</view>
			</view>
		</view>
		
		<!-- 调解信息 -->
		<view class="info-section">
			<view class="section-title">调解信息</view>
			<view class="info-item">
				<text class="info-label">债权人</text>
				<text class="info-value">{{workOrderData.creditor || '某银行信用卡中心'}}</text>
			</view>
			<view class="info-item">
				<text class="info-label">债务类型</text>
				<text class="info-value">{{workOrderData.debtType || '信用卡欠款'}}</text>
			</view>
			<view class="info-item">
				<text class="info-label">欠款金额</text>
				<text class="info-value">¥{{workOrderData.amount || '50,000.00'}}</text>
			</view>
			<view class="info-item">
				<text class="info-label">欠款时间</text>
				<text class="info-value">{{workOrderData.debtDate || '2022-05-01'}} 至今</text>
			</view>
		</view>
		<!-- 文件列表 -->
		<view class="info-section">
			<view class="section-title">相关文件</view>
			<view class="file-item" v-for="file in workOrderData.files" :key="file.id">
				<!-- 根据file.name的扩展名,判断是pdf还是图片 -->
				<i class="fas " :class="file.name.split('.').pop() === 'pdf' ? 'fa-file-pdf' : 'fa-file-image'" :style="{color: file.name.split('.').pop() === 'pdf' ? '#ff4d4f' : '#52c41a'}"></i>
				<view class="file-content"><text class="file-name">{{file.name}}</text></view>
			</view>
		</view>

		<view class="info-section work-closing" v-if="workOrderData.case_status_cn === '已关闭'">
			<view class="section-title">
				<i class="fas fa-info-circle"></i>关闭原因
			</view>
			<view class="section-tip">
				<text>{{workOrderData.closingReason || '调解案件已超过规定期限。'}}</text>
			</view>
		</view>
		<!-- 底部操作按钮 -->
		<view class="action-buttons" v-else>
			<button class="accept-button" @click="handleAccept"><i class="fas fa-check-circle"></i>接受调解</button>
			<button class="reject-button" @click="handleReject"><i class="fas fa-clock"></i>考虑一下</button>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { api } from '@/utils/api.js';

// 接收参数
const orderId = ref('');
const initiateDate = ref('');
const orderStatus = ref('');

// 调解数据
const workOrderData = ref({
	case_number: 'MED20230001',
	case_status_cn: '待确认',
	initiate_date: '2023-11-01',
	creditor: '某银行信用卡中心',
	debtType: '信用卡欠款',
	amount: '50,000.00',
	debtDate: '2022-05-01',
	files: [
		{
			id: 1,
			name: '债权转让协议书.pdf'
		},{
			id: 2,
			name: '借款合同扫描件.jpg'
		},{
			id: 3,
			name: '身份证明材料.png'
		}
	]
});

// 页面加载时获取参数 - uni-app标准方式
// onLoad是uni-app专门用于接收页面参数的生命周期钩子
onLoad((options) => {
	console.log('页面参数:', options);
	
	// 获取调解案件ID参数
	if (options.case_number) {
		orderId.value = options.case_number;
		console.log('接收到调解ID:', orderId.value);
	}
	// 获取创建日期参数
	if (options.initiate_date) {
		initiateDate.value = options.initiate_date;
		console.log('接收到日期:', initiateDate.value);
	}
	
	// 获取调解状态参数
	if (options.case_status_cn) {
		orderStatus.value = options.case_status_cn;
		console.log('接收到调解状态:', orderStatus.value);
		
		// 只有当status参数为"已关闭"时，才将其赋值给workOrderData.case_status_cn
		// 这样可以确保已关闭的案件能正确显示关闭状态和相关信息
		if (orderStatus.value === '已关闭') {
			// 将URL传递的状态赋值给工单数据的状态字段
			workOrderData.value.case_status_cn = orderStatus.value;
			console.log('检测到已关闭状态，已更新workOrderData状态为:', orderStatus.value);
		}
	}
	
	// 获取详细的工单数据
	if (orderId.value) {
		fetchWorkOrderDetail(orderId.value);
	} else {
		// 如果没有传递ID参数，使用默认的模拟数据
		fetchWorkOrderDetail();
	}
});

// 组件挂载后的初始化逻辑
onMounted(() => {
	console.log('工单详情页面组件已挂载');
	// 注意：参数获取逻辑已移至onLoad中，这里只保留组件挂载后的其他初始化操作
});

// 获取调解确认数据
const fetchWorkOrderDetail = (id) => {
	if (id) {
		// 使用API获取数据
		uni.showLoading({
			title: '加载中...'
		});
		
		api.workOrder.getDetail(id)
			.then(res => {
				uni.hideLoading();
				if (res.code === 0) {
					workOrderData.value = res.data;
				} else {
					uni.showToast({
						title: res.message || '获取调解确认失败',
						icon: 'none'
					});
				}
			})
			.catch(err => {
				uni.hideLoading();
				console.error('获取调解确认失败', err);
				uni.showToast({
					title: '获取调解确认失败',
					icon: 'none'
				});
			});
	} else {
		// 使用模拟数据
		setTimeout(() => {
			console.log('调解确认数据已加载（模拟）');
		}, 500);
	}
};

// 处理接受调解
const handleAccept = () => {
	// 显示确认对话框
	uni.showModal({
		title: '确认接受',
		content: '您确定要接受此调解吗？',
		success: (res) => {
			if (res.confirm) {
				// 用户点击确定
				uni.showLoading({
					title: '处理中...'
				});
				
				// 调用API接受调解
				uni.navigateTo({
					url: `/pages/solution_confirm/solution_confirm?orderId=${workOrderData.value.id}`,
					success: () => {
						console.log('跳转到调解方案确认页面');
					},
					fail: (err) => {
						console.error('跳转失败', err);
						uni.showToast({
							title: '跳转失败',
							icon: 'none'
						});
					}
				});
				/* if (orderId.value) {
					api.workOrder.acceptWorkOrder(orderId.value)
						.then(res => {
							uni.hideLoading();
							if (res.code === 0) {
								uni.showToast({
									title: '调解已接受',
									icon: 'success',
									duration: 1500
								});
								
								// 成功后跳转到调解方案确认页面
								setTimeout(() => {
									uni.navigateTo({
										url: `/pages/solution_confirm/solution_confirm?orderId=${workOrderData.value.id}`,
										success: () => {
											console.log('跳转到调解方案确认页面');
										},
										fail: (err) => {
											console.error('跳转失败', err);
											uni.showToast({
												title: '跳转失败',
												icon: 'none'
											});
										}
									});
								}, 1500);
							} else {
								uni.showToast({
									title: res.message || '操作失败',
									icon: 'none'
								});
							}
						})
						.catch(err => {
							uni.hideLoading();
							console.error('接受调解失败', err);
							uni.showToast({
								title: '接受调解失败',
								icon: 'none'
							});
						});
				} else {
					// 模拟API调用
					setTimeout(() => {
						uni.hideLoading();
						
						uni.showToast({
							title: '调解已接受',
							icon: 'success',
							duration: 1500
						});
						
						// 成功后跳转到调解方案确认页面
						setTimeout(() => {
							uni.navigateTo({
								url: `/pages/solution_confirm/solution_confirm?orderId=${workOrderData.value.id}`,
								success: () => {
									console.log('跳转到调解方案确认页面');
								},
								fail: (err) => {
									console.error('跳转失败', err);
									uni.showToast({
										title: '跳转失败',
										icon: 'none'
									});
								}
							});
						}, 1500);
					}, 1000);
				} */
			}
		}
	});
};

// 考虑一下
const handleReject = () => {
	uni.navigateTo({
		url: `/pages/mediation_query/mediation_query`,
	});
	// 显示确认对话框
	/* uni.showModal({
		title: '确认拒绝',
		content: '您确定要拒绝此调解吗？',
		success: (res) => {
			if (res.confirm) {
				// 用户点击确定
				uni.showLoading({
					title: '处理中...'
				});
				
				// 调用API拒绝调解
				if (orderId.value) {
					api.workOrder.rejectWorkOrder(orderId.value)
						.then(res => {
							uni.hideLoading();
							if (res.code === 0) {
								uni.showToast({
									title: '调解已拒绝',
									icon: 'success',
									duration: 1500
								});
								
								// 成功后跳转到首页
								setTimeout(() => {
									uni.switchTab({
										url: '/pages/index/index',
										success: () => {
											console.log('跳转到首页');
										},
										fail: (err) => {
											console.error('跳转失败', err);
											uni.showToast({
												title: '跳转失败',
												icon: 'none'
											});
										}
									});
								}, 1500);
							} else {
								uni.showToast({
									title: res.message || '操作失败',
									icon: 'none'
								});
							}
						})
						.catch(err => {
							uni.hideLoading();
							console.error('拒绝调解失败', err);
							uni.showToast({
								title: '拒绝调解失败',
								icon: 'none'
							});
						});
				} else {
					// 模拟API调用
					setTimeout(() => {
						uni.hideLoading();
						
						uni.showToast({
							title: '调解已拒绝',
							icon: 'success',
							duration: 1500
						});
						
						// 成功后跳转到首页
						setTimeout(() => {
							uni.switchTab({
								url: '/pages/index/index',
								success: () => {
									console.log('跳转到首页');
								},
								fail: (err) => {
									console.error('跳转失败', err);
									uni.showToast({
										title: '跳转失败',
										icon: 'none'
									});
								}
							});
						}, 1500);
					}, 1000);
				}
			}
		}
	}); */
};


</script>

<style lang="scss">
.work-order-detail-container {
	min-height: 100vh;
	background-color: #f5f5f5;
	padding: 30rpx;
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

.work-order-card {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.work-order-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.work-order-title {
	font-size: 30rpx;
	color: #333;
	font-weight: bold;
}

.work-order-status {
	padding: 6rpx 20rpx;
	border-radius: 30rpx;
}

.status-label {
	font-size: 26rpx;
	padding: 8rpx 20rpx;
	border-radius: 30rpx;
	background-color: #999;
	color: #fff;
}

.status-pending {
	background-color: #faad14;
}

.work-order-date {
	font-size: 28rpx;
	color: #666;
}

/* .progress-bar {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
} */

.progress-steps {
	display: flex;
	justify-content: space-between;
	position: relative;
}

.progress-step {
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
	position: relative;
}

.step-circle {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background-color: #e0e0e0;
	color: #fff;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
	position: relative;
	z-index: 2;
}

.step-line {
	position: absolute;
	top: 30rpx;
	left: 50%;
	right: -50%;
	height: 4rpx;
	background-color: #e0e0e0;
	z-index: 1;
}

.progress-step:last-child .step-line {
	display: none;
}

.step-label {
	font-size: 24rpx;
	color: #999;
	text-align: center;
}

.progress-step.active .step-circle {
	background-color: #2979ff;
}

.progress-step.active .step-label {
	color: #2979ff;
	font-weight: bold;
}

.info-section {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	position: relative;
}

/* .section-title::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 8rpx;
	height: 32rpx;
	background-color: #2979ff;
	border-radius: 4rpx;
} */

.info-item {
	display: flex;
	flex-direction: column;
    align-items: flex-start;
    gap: 16rpx;
	padding: 20rpx 0;
	color: #333;
	font-size: 32rpx;
}

.info-item:last-child {
	border-bottom: none;
}

.info-label {
	font-weight: 500;
}

.file-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 28rpx 0;
    border-bottom: 2rpx solid rgba(0, 0, 0, 0.06);
    transition: 0.3s;
	.fas{
		margin-right: 24rpx;
	}
}
.file-item:last-child {
    border-bottom: none;
}
.file-content {
    min-width: 0;
    display: flex;
    align-items: center;
    flex: 1 1 0%;
}
.file-name {
    font-size: 30rpx;
    font-weight: 500;
    color: #333;
    line-height: 1.3;
    word-break: break-all;
    transition: color 0.3s;
}

.action-buttons {
	display: flex;
	gap: 30rpx;
}

.reject-button,
.accept-button {
	flex: 1;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
	border-radius: 16rpx;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 4px;
	font-size: 32rpx;
}

.reject-button {
	background-color: #fff;
	color: #3b7eeb;
	border: 2rpx solid #3b7eeb;
}

.accept-button {
	background-color: #3b7eeb;
	color: #fff;
}
.work-closing{
	background-color: rgb(255, 248, 248);
    border:2rpx solid rgb(255, 237, 237);
	.section-title{
		font-size: 36rpx;
		color:#f5222d;
		font-weight: bold;
		display: flex;
		align-items: center;
		.fas{
			color: #f5222d;
			font-size: 40rpx;
			margin-right: 20rpx;
		}
	}
	.section-tip{
		font-size: 32rpx;
		color:#666;
	}
}
</style> 