{"version": 3, "file": "work_order_detail.js", "sources": ["pages/work_order_detail/work_order_detail.vue", "C:/software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvd29ya19vcmRlcl9kZXRhaWwvd29ya19vcmRlcl9kZXRhaWwudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"work-order-detail-container\">\r\n\t\t<!-- 案件基本信息 -->\r\n\t\t<view class=\"work-order-card\">\r\n\t\t\t<view class=\"work-order-header\">\r\n\t\t\t\t<view class=\"work-order-title\">\r\n\t\t\t\t\t<text>调解案件号: </text>\r\n\t\t\t\t\t<text class=\"work-order-id\">{{workOrderData.case_number}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"work-order-status\">\r\n\t\t\t\t\t<text class=\"status-label\" :class=\"{'status-pending': workOrderData.case_status_cn === '待确认'}\">{{workOrderData.case_status_cn || '待确认'}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"work-order-date\">发起日期: {{workOrderData.initiate_date || '2023-11-01'}}</view>\r\n\t\t\t<view class=\"work-order-date\" v-if=\"workOrderData.case_status_cn === '已关闭'\">关闭日期: {{workOrderData.closingDate || '2023-12-01'}}</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 进度条 -->\r\n\t\t<view class=\"progress-bar\">\r\n\t\t\t<view class=\"progress-steps\">\r\n\t\t\t\t<view class=\"progress-step active\">\r\n\t\t\t\t\t<view class=\"step-circle\">1</view>\r\n\t\t\t\t\t<view class=\"step-line\"></view>\r\n\t\t\t\t\t<view class=\"step-label\">调解确认</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"progress-step\">\r\n\t\t\t\t\t<view class=\"step-circle\">2</view>\r\n\t\t\t\t\t<view class=\"step-line\"></view>\r\n\t\t\t\t\t<view class=\"step-label\">方案确认</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"progress-step\">\r\n\t\t\t\t\t<view class=\"step-circle\">3</view>\r\n\t\t\t\t\t<view class=\"step-line\"></view>\r\n\t\t\t\t\t<view class=\"step-label\">协议签署</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"progress-step\">\r\n\t\t\t\t\t<view class=\"step-circle\">4</view>\r\n\t\t\t\t\t<view class=\"step-label\">完成</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 调解信息 -->\r\n\t\t<view class=\"info-section\">\r\n\t\t\t<view class=\"section-title\">调解信息</view>\r\n\t\t\t<view class=\"info-item\">\r\n\t\t\t\t<text class=\"info-label\">债权人</text>\r\n\t\t\t\t<text class=\"info-value\">{{workOrderData.creditor || '某银行信用卡中心'}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-item\">\r\n\t\t\t\t<text class=\"info-label\">债务类型</text>\r\n\t\t\t\t<text class=\"info-value\">{{workOrderData.debtType || '信用卡欠款'}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-item\">\r\n\t\t\t\t<text class=\"info-label\">欠款金额</text>\r\n\t\t\t\t<text class=\"info-value\">¥{{workOrderData.amount || '50,000.00'}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-item\">\r\n\t\t\t\t<text class=\"info-label\">欠款时间</text>\r\n\t\t\t\t<text class=\"info-value\">{{workOrderData.debtDate || '2022-05-01'}} 至今</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 文件列表 -->\r\n\t\t<view class=\"info-section\">\r\n\t\t\t<view class=\"section-title\">相关文件</view>\r\n\t\t\t<view class=\"file-item\" v-for=\"file in workOrderData.files\" :key=\"file.id\">\r\n\t\t\t\t<!-- 根据file.name的扩展名,判断是pdf还是图片 -->\r\n\t\t\t\t<i class=\"fas \" :class=\"file.name.split('.').pop() === 'pdf' ? 'fa-file-pdf' : 'fa-file-image'\" :style=\"{color: file.name.split('.').pop() === 'pdf' ? '#ff4d4f' : '#52c41a'}\"></i>\r\n\t\t\t\t<view class=\"file-content\"><text class=\"file-name\">{{file.name}}</text></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"info-section work-closing\" v-if=\"workOrderData.case_status_cn === '已关闭'\">\r\n\t\t\t<view class=\"section-title\">\r\n\t\t\t\t<i class=\"fas fa-info-circle\"></i>关闭原因\r\n\t\t\t</view>\r\n\t\t\t<view class=\"section-tip\">\r\n\t\t\t\t<text>{{workOrderData.closingReason || '调解案件已超过规定期限。'}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 底部操作按钮 -->\r\n\t\t<view class=\"action-buttons\" v-else>\r\n\t\t\t<button class=\"accept-button\" @click=\"handleAccept\"><i class=\"fas fa-check-circle\"></i>接受调解</button>\r\n\t\t\t<button class=\"reject-button\" @click=\"handleReject\"><i class=\"fas fa-clock\"></i>考虑一下</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted } from 'vue';\r\nimport { onLoad } from '@dcloudio/uni-app';\r\nimport { api } from '@/utils/api.js';\r\n\r\n// 接收参数\r\nconst orderId = ref('');\r\nconst initiateDate = ref('');\r\nconst orderStatus = ref('');\r\n\r\n// 调解数据\r\nconst workOrderData = ref({\r\n\tcase_number: 'MED20230001',\r\n\tcase_status_cn: '待确认',\r\n\tinitiate_date: '2023-11-01',\r\n\tcreditor: '某银行信用卡中心',\r\n\tdebtType: '信用卡欠款',\r\n\tamount: '50,000.00',\r\n\tdebtDate: '2022-05-01',\r\n\tfiles: [\r\n\t\t{\r\n\t\t\tid: 1,\r\n\t\t\tname: '债权转让协议书.pdf'\r\n\t\t},{\r\n\t\t\tid: 2,\r\n\t\t\tname: '借款合同扫描件.jpg'\r\n\t\t},{\r\n\t\t\tid: 3,\r\n\t\t\tname: '身份证明材料.png'\r\n\t\t}\r\n\t]\r\n});\r\n\r\n// 页面加载时获取参数 - uni-app标准方式\r\n// onLoad是uni-app专门用于接收页面参数的生命周期钩子\r\nonLoad((options) => {\r\n\tconsole.log('页面参数:', options);\r\n\t\r\n\t// 获取调解案件ID参数\r\n\tif (options.case_number) {\r\n\t\torderId.value = options.case_number;\r\n\t\tconsole.log('接收到调解ID:', orderId.value);\r\n\t}\r\n\t// 获取创建日期参数\r\n\tif (options.initiate_date) {\r\n\t\tinitiateDate.value = options.initiate_date;\r\n\t\tconsole.log('接收到日期:', initiateDate.value);\r\n\t}\r\n\t\r\n\t// 获取调解状态参数\r\n\tif (options.case_status_cn) {\r\n\t\torderStatus.value = options.case_status_cn;\r\n\t\tconsole.log('接收到调解状态:', orderStatus.value);\r\n\t\t\r\n\t\t// 只有当status参数为\"已关闭\"时，才将其赋值给workOrderData.case_status_cn\r\n\t\t// 这样可以确保已关闭的案件能正确显示关闭状态和相关信息\r\n\t\tif (orderStatus.value === '已关闭') {\r\n\t\t\t// 将URL传递的状态赋值给工单数据的状态字段\r\n\t\t\tworkOrderData.value.case_status_cn = orderStatus.value;\r\n\t\t\tconsole.log('检测到已关闭状态，已更新workOrderData状态为:', orderStatus.value);\r\n\t\t}\r\n\t}\r\n\t\r\n\t// 获取详细的工单数据\r\n\tif (orderId.value) {\r\n\t\tfetchWorkOrderDetail(orderId.value);\r\n\t} else {\r\n\t\t// 如果没有传递ID参数，使用默认的模拟数据\r\n\t\tfetchWorkOrderDetail();\r\n\t}\r\n});\r\n\r\n// 组件挂载后的初始化逻辑\r\nonMounted(() => {\r\n\tconsole.log('工单详情页面组件已挂载');\r\n\t// 注意：参数获取逻辑已移至onLoad中，这里只保留组件挂载后的其他初始化操作\r\n});\r\n\r\n// 获取调解确认数据\r\nconst fetchWorkOrderDetail = (id) => {\r\n\tif (id) {\r\n\t\t// 使用API获取数据\r\n\t\tuni.showLoading({\r\n\t\t\ttitle: '加载中...'\r\n\t\t});\r\n\t\t\r\n\t\tapi.workOrder.getDetail(id)\r\n\t\t\t.then(res => {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tif (res.code === 0) {\r\n\t\t\t\t\tworkOrderData.value = res.data;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: res.message || '获取调解确认失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch(err => {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tconsole.error('获取调解确认失败', err);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '获取调解确认失败',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t} else {\r\n\t\t// 使用模拟数据\r\n\t\tsetTimeout(() => {\r\n\t\t\tconsole.log('调解确认数据已加载（模拟）');\r\n\t\t}, 500);\r\n\t}\r\n};\r\n\r\n// 处理接受调解\r\nconst handleAccept = () => {\r\n\t// 显示确认对话框\r\n\tuni.showModal({\r\n\t\ttitle: '确认接受',\r\n\t\tcontent: '您确定要接受此调解吗？',\r\n\t\tsuccess: (res) => {\r\n\t\t\tif (res.confirm) {\r\n\t\t\t\t// 用户点击确定\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '处理中...'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 调用API接受调解\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/solution_confirm/solution_confirm?orderId=${workOrderData.value.id}`,\r\n\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\tconsole.log('跳转到调解方案确认页面');\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error('跳转失败', err);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '跳转失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t/* if (orderId.value) {\r\n\t\t\t\t\tapi.workOrder.acceptWorkOrder(orderId.value)\r\n\t\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tif (res.code === 0) {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '调解已接受',\r\n\t\t\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t// 成功后跳转到调解方案确认页面\r\n\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\turl: `/pages/solution_confirm/solution_confirm?orderId=${workOrderData.value.id}`,\r\n\t\t\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log('跳转到调解方案确认页面');\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.error('跳转失败', err);\r\n\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '跳转失败',\r\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}, 1500);\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: res.message || '操作失败',\r\n\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tconsole.error('接受调解失败', err);\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '接受调解失败',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 模拟API调用\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '调解已接受',\r\n\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 成功后跳转到调解方案确认页面\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: `/pages/solution_confirm/solution_confirm?orderId=${workOrderData.value.id}`,\r\n\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\tconsole.log('跳转到调解方案确认页面');\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\t\tconsole.error('跳转失败', err);\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '跳转失败',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}, 1500);\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t} */\r\n\t\t\t}\r\n\t\t}\r\n\t});\r\n};\r\n\r\n// 考虑一下\r\nconst handleReject = () => {\r\n\tuni.navigateTo({\r\n\t\turl: `/pages/mediation_query/mediation_query`,\r\n\t});\r\n\t// 显示确认对话框\r\n\t/* uni.showModal({\r\n\t\ttitle: '确认拒绝',\r\n\t\tcontent: '您确定要拒绝此调解吗？',\r\n\t\tsuccess: (res) => {\r\n\t\t\tif (res.confirm) {\r\n\t\t\t\t// 用户点击确定\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '处理中...'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 调用API拒绝调解\r\n\t\t\t\tif (orderId.value) {\r\n\t\t\t\t\tapi.workOrder.rejectWorkOrder(orderId.value)\r\n\t\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tif (res.code === 0) {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '调解已拒绝',\r\n\t\t\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t// 成功后跳转到首页\r\n\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\t\turl: '/pages/index/index',\r\n\t\t\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log('跳转到首页');\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.error('跳转失败', err);\r\n\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '跳转失败',\r\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}, 1500);\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: res.message || '操作失败',\r\n\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tconsole.error('拒绝调解失败', err);\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '拒绝调解失败',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 模拟API调用\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '调解已拒绝',\r\n\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 成功后跳转到首页\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\turl: '/pages/index/index',\r\n\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\tconsole.log('跳转到首页');\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\t\tconsole.error('跳转失败', err);\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '跳转失败',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}, 1500);\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}); */\r\n};\r\n\r\n\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.work-order-detail-container {\r\n\tmin-height: 100vh;\r\n\tbackground-color: #f5f5f5;\r\n\tpadding: 30rpx;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 30rpx;\r\n}\r\n\r\n.work-order-card {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 30rpx;\r\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.work-order-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.work-order-title {\r\n\tfont-size: 30rpx;\r\n\tcolor: #333;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.work-order-status {\r\n\tpadding: 6rpx 20rpx;\r\n\tborder-radius: 30rpx;\r\n}\r\n\r\n.status-label {\r\n\tfont-size: 26rpx;\r\n\tpadding: 8rpx 20rpx;\r\n\tborder-radius: 30rpx;\r\n\tbackground-color: #999;\r\n\tcolor: #fff;\r\n}\r\n\r\n.status-pending {\r\n\tbackground-color: #faad14;\r\n}\r\n\r\n.work-order-date {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n/* .progress-bar {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 30rpx;\r\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n} */\r\n\r\n.progress-steps {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tposition: relative;\r\n}\r\n\r\n.progress-step {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tflex: 1;\r\n\tposition: relative;\r\n}\r\n\r\n.step-circle {\r\n\twidth: 60rpx;\r\n\theight: 60rpx;\r\n\tborder-radius: 50%;\r\n\tbackground-color: #e0e0e0;\r\n\tcolor: #fff;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n\tmargin-bottom: 20rpx;\r\n\tposition: relative;\r\n\tz-index: 2;\r\n}\r\n\r\n.step-line {\r\n\tposition: absolute;\r\n\ttop: 30rpx;\r\n\tleft: 50%;\r\n\tright: -50%;\r\n\theight: 4rpx;\r\n\tbackground-color: #e0e0e0;\r\n\tz-index: 1;\r\n}\r\n\r\n.progress-step:last-child .step-line {\r\n\tdisplay: none;\r\n}\r\n\r\n.step-label {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n\ttext-align: center;\r\n}\r\n\r\n.progress-step.active .step-circle {\r\n\tbackground-color: #2979ff;\r\n}\r\n\r\n.progress-step.active .step-label {\r\n\tcolor: #2979ff;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.info-section {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 30rpx;\r\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.section-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tmargin-bottom: 20rpx;\r\n\tposition: relative;\r\n}\r\n\r\n/* .section-title::before {\r\n\tcontent: '';\r\n\tposition: absolute;\r\n\tleft: 0;\r\n\ttop: 50%;\r\n\ttransform: translateY(-50%);\r\n\twidth: 8rpx;\r\n\theight: 32rpx;\r\n\tbackground-color: #2979ff;\r\n\tborder-radius: 4rpx;\r\n} */\r\n\r\n.info-item {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 16rpx;\r\n\tpadding: 20rpx 0;\r\n\tcolor: #333;\r\n\tfont-size: 32rpx;\r\n}\r\n\r\n.info-item:last-child {\r\n\tborder-bottom: none;\r\n}\r\n\r\n.info-label {\r\n\tfont-weight: 500;\r\n}\r\n\r\n.file-item {\r\n    display: flex;\r\n    align-items: center;\r\n    cursor: pointer;\r\n    padding: 28rpx 0;\r\n    border-bottom: 2rpx solid rgba(0, 0, 0, 0.06);\r\n    transition: 0.3s;\r\n\t.fas{\r\n\t\tmargin-right: 24rpx;\r\n\t}\r\n}\r\n.file-item:last-child {\r\n    border-bottom: none;\r\n}\r\n.file-content {\r\n    min-width: 0;\r\n    display: flex;\r\n    align-items: center;\r\n    flex: 1 1 0%;\r\n}\r\n.file-name {\r\n    font-size: 30rpx;\r\n    font-weight: 500;\r\n    color: #333;\r\n    line-height: 1.3;\r\n    word-break: break-all;\r\n    transition: color 0.3s;\r\n}\r\n\r\n.action-buttons {\r\n\tdisplay: flex;\r\n\tgap: 30rpx;\r\n}\r\n\r\n.reject-button,\r\n.accept-button {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\tflex-direction: row;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tgap: 16rpx;\r\n\tborder-radius: 16rpx;\r\n    box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 4px;\r\n\tfont-size: 32rpx;\r\n}\r\n\r\n.reject-button {\r\n\tbackground-color: #fff;\r\n\tcolor: #3b7eeb;\r\n\tborder: 2rpx solid #3b7eeb;\r\n}\r\n\r\n.accept-button {\r\n\tbackground-color: #3b7eeb;\r\n\tcolor: #fff;\r\n}\r\n.work-closing{\r\n\tbackground-color: rgb(255, 248, 248);\r\n    border:2rpx solid rgb(255, 237, 237);\r\n\t.section-title{\r\n\t\tfont-size: 36rpx;\r\n\t\tcolor:#f5222d;\r\n\t\tfont-weight: bold;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\t.fas{\r\n\t\t\tcolor: #f5222d;\r\n\t\t\tfont-size: 40rpx;\r\n\t\t\tmargin-right: 20rpx;\r\n\t\t}\r\n\t}\r\n\t.section-tip{\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor:#666;\r\n\t}\r\n}\r\n</style> ", "import MiniProgramPage from 'D:/work/不良资产系统/non-performing-assets/pages/work_order_detail/work_order_detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onLoad", "uni", "onMounted", "api", "MiniProgramPage"], "mappings": ";;;;;;AA8FA,UAAM,UAAUA,cAAAA,IAAI,EAAE;AACtB,UAAM,eAAeA,cAAAA,IAAI,EAAE;AAC3B,UAAM,cAAcA,cAAAA,IAAI,EAAE;AAG1B,UAAM,gBAAgBA,cAAAA,IAAI;AAAA,MACzB,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,UAAU;AAAA,MACV,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,OAAO;AAAA,QACN;AAAA,UACC,IAAI;AAAA,UACJ,MAAM;AAAA,QACT;AAAA,QAAI;AAAA,UACD,IAAI;AAAA,UACJ,MAAM;AAAA,QACT;AAAA,QAAI;AAAA,UACD,IAAI;AAAA,UACJ,MAAM;AAAA,QACN;AAAA,MACD;AAAA,IACF,CAAC;AAIDC,kBAAM,OAAC,CAAC,YAAY;AACnBC,+FAAY,SAAS,OAAO;AAG5B,UAAI,QAAQ,aAAa;AACxB,gBAAQ,QAAQ,QAAQ;AACxBA,sBAAA,MAAA,MAAA,OAAA,wDAAY,YAAY,QAAQ,KAAK;AAAA,MACrC;AAED,UAAI,QAAQ,eAAe;AAC1B,qBAAa,QAAQ,QAAQ;AAC7BA,sBAAA,MAAA,MAAA,OAAA,wDAAY,UAAU,aAAa,KAAK;AAAA,MACxC;AAGD,UAAI,QAAQ,gBAAgB;AAC3B,oBAAY,QAAQ,QAAQ;AAC5BA,sBAAA,MAAA,MAAA,OAAA,wDAAY,YAAY,YAAY,KAAK;AAIzC,YAAI,YAAY,UAAU,OAAO;AAEhC,wBAAc,MAAM,iBAAiB,YAAY;AACjDA,wBAAA,MAAA,MAAA,OAAA,wDAAY,iCAAiC,YAAY,KAAK;AAAA,QAC9D;AAAA,MACD;AAGD,UAAI,QAAQ,OAAO;AAClB,6BAAqB,QAAQ,KAAK;AAAA,MACpC,OAAQ;AAEN;MACA;AAAA,IACF,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AACfD,oBAAAA,2EAAY,aAAa;AAAA,IAE1B,CAAC;AAGD,UAAM,uBAAuB,CAAC,OAAO;AACpC,UAAI,IAAI;AAEPA,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO;AAAA,QACV,CAAG;AAEDE,sBAAI,UAAU,UAAU,EAAE,EACxB,KAAK,SAAO;AACZF,wBAAG,MAAC,YAAW;AACf,cAAI,IAAI,SAAS,GAAG;AACnB,0BAAc,QAAQ,IAAI;AAAA,UAC/B,OAAW;AACNA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO,IAAI,WAAW;AAAA,cACtB,MAAM;AAAA,YACZ,CAAM;AAAA,UACD;AAAA,QACL,CAAI,EACA,MAAM,SAAO;AACbA,wBAAG,MAAC,YAAW;AACfA,wBAAA,MAAA,MAAA,SAAA,wDAAc,YAAY,GAAG;AAC7BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACX,CAAK;AAAA,QACL,CAAI;AAAA,MACJ,OAAQ;AAEN,mBAAW,MAAM;AAChBA,wBAAAA,MAAY,MAAA,OAAA,wDAAA,eAAe;AAAA,QAC3B,GAAE,GAAG;AAAA,MACN;AAAA,IACF;AAGA,UAAM,eAAe,MAAM;AAE1BA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAEhBA,0BAAAA,MAAI,YAAY;AAAA,cACf,OAAO;AAAA,YACZ,CAAK;AAGDA,0BAAAA,MAAI,WAAW;AAAA,cACd,KAAK,oDAAoD,cAAc,MAAM,EAAE;AAAA,cAC/E,SAAS,MAAM;AACdA,8BAAAA,MAAY,MAAA,OAAA,wDAAA,aAAa;AAAA,cACzB;AAAA,cACD,MAAM,CAAC,QAAQ;AACdA,8BAAA,MAAA,MAAA,SAAA,wDAAc,QAAQ,GAAG;AACzBA,8BAAAA,MAAI,UAAU;AAAA,kBACb,OAAO;AAAA,kBACP,MAAM;AAAA,gBACb,CAAO;AAAA,cACD;AAAA,YACN,CAAK;AAAA,UAwED;AAAA,QACD;AAAA,MACH,CAAE;AAAA,IACF;AAGA,UAAM,eAAe,MAAM;AAC1BA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACP,CAAE;AAAA,IAuFF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3YA,GAAG,WAAWG,SAAe;"}